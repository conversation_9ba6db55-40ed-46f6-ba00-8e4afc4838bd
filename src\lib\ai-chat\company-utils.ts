/**
 * Company Utilities Module
 * 
 * This module provides utility functions for company-related operations including:
 * - Random investment recommendation generation
 * - Company data lookup and validation
 * - Pattern matching for user responses
 * - Company name translation and formatting
 */

import { QUICK_ENRICHED_FINAL as DATA } from '@/data/sp500_enriched_final';
import { RandomRecommendation, CompanyRecommendation, CompanyData } from './types';
import { PATTERNS, OPENAI_CONFIG } from './config';
import { translateDescription } from './ai-service';
import { getAllAvailableIndustries } from './rag-service';
import { getOpenAIClient } from '../openai-client';

// ============================================================================
// Direct Ticker Matching (Bypass GPT for Known Tickers)
// ============================================================================

/**
 * Checks if input is a direct ticker symbol and returns company info
 * This bypasses GPT classification entirely for known tickers
 */
export function getDirectTickerMatch(input: string): { ticker: string; name: string; industry: string } | null {
  const normalizedInput = input.trim().toUpperCase();

  // Check if input matches a ticker exactly
  if (DATA[normalizedInput as keyof typeof DATA]) {
    const company = DATA[normalizedInput as keyof typeof DATA];
    console.log(`🎯 Direct ticker match: ${normalizedInput} -> ${company.name}`);
    return {
      ticker: normalizedInput,
      name: company.name,
      industry: company.industry
    };
  }

  return null;
}

// ============================================================================
// OpenAI Client for Response Classification
// ============================================================================
// OpenAI client is now managed by the centralized client factory

// ============================================================================
// Pattern Matching Utilities
// ============================================================================

/**
 * Checks if text matches positive response patterns using GPT-based classification
 */
export async function isPositive(text: string): Promise<boolean> {
  try {
    const result = await classifyResponseType(text);
    return result.type === 'positive';
  } catch (error) {
    console.error('GPT positive response classification failed, using fallback:', error);
    // Fallback to basic pattern matching if GPT fails
    return PATTERNS.positive.test(text.trim());
  }
}

/**
 * Checks if text matches negative response patterns using GPT-based classification
 */
export async function isNegative(text: string): Promise<boolean> {
  try {
    const result = await classifyResponseType(text);
    return result.type === 'negative';
  } catch (error) {
    console.error('GPT negative response classification failed, using fallback:', error);
    // Fallback to basic pattern matching if GPT fails
    return PATTERNS.negative.test(text.trim());
  }
}

/**
 * GPT-based response type classification
 */
async function classifyResponseType(text: string): Promise<{
  type: 'positive' | 'negative' | 'neutral';
  confidence: number;
  reasoning: string;
}> {
  const prompt = `다음 사용자 응답이 긍정적인지, 부정적인지, 중립적인지 분류해주세요.

사용자 응답: "${text}"

분류 기준:
- positive: 동의, 승인, 긍정적 응답 (네, 예, 응, 좋아, 맞아, 그래, yes, ok, y 등)
- negative: 거부, 부정, 부정적 응답 (아니, 아니요, 싫어, 안돼, no, n, nope 등)
- neutral: 명확하지 않거나 중립적인 응답

응답 형식 (JSON):
{
  "type": "positive/negative/neutral",
  "confidence": 0.0-1.0,
  "reasoning": "분류 이유"
}`;

  const response = await getOpenAIClient('COMPANY_UTILS').chat.completions.create({
    model: OPENAI_CONFIG.model,
    messages: [
      {
        role: 'system',
        content: '당신은 사용자 응답 분류 전문가입니다. 긍정/부정/중립을 정확하게 판단하세요.'
      },
      {
        role: 'user',
        content: prompt
      }
    ],
    temperature: OPENAI_CONFIG.temperature.classification,
    max_tokens: OPENAI_CONFIG.maxTokens.classification,
  });

  const aiResponse = response.choices[0].message.content?.trim();

  if (!aiResponse) {
    throw new Error('GPT response type classification returned empty response');
  }

  try {
    // First attempt: direct JSON parsing
    const parsed = JSON.parse(aiResponse);
    return {
      type: parsed.type || 'neutral',
      confidence: Math.max(0, Math.min(1, parsed.confidence || 0.5)),
      reasoning: parsed.reasoning || 'GPT 분석 결과'
    };
  } catch (parseError) {
    console.error('Failed to parse response type classification JSON:', aiResponse);
    console.error('Parse error:', parseError);

    // Enhanced fallback: handle truncated Korean text and malformed JSON
    try {
      // Try to fix common JSON issues before parsing
      let fixedResponse = aiResponse;

      // Fix unterminated strings in reasoning field (common with Korean text)
      const reasoningMatch = fixedResponse.match(/"reasoning"\s*:\s*"([^"]*?)(?:$|")/);
      if (reasoningMatch && !fixedResponse.includes('"reasoning":"' + reasoningMatch[1] + '"')) {
        // If reasoning field is unterminated, close it
        fixedResponse = fixedResponse.replace(
          /"reasoning"\s*:\s*"([^"]*?)$/,
          '"reasoning":"$1"'
        );
        // Ensure proper JSON closure
        if (!fixedResponse.trim().endsWith('}')) {
          fixedResponse = fixedResponse.trim() + '}';
        }
      }

      // Try parsing the fixed JSON
      try {
        const fixedParsed = JSON.parse(fixedResponse);
        console.log(`✅ Fixed JSON parsing successful`);
        return {
          type: fixedParsed.type || 'neutral',
          confidence: Math.max(0, Math.min(1, fixedParsed.confidence || 0.5)),
          reasoning: fixedParsed.reasoning || 'Fixed JSON parsing'
        };
      } catch (fixedParseError) {
        // If fixed parsing fails, fall back to regex extraction
        console.log('Fixed JSON parsing failed, using regex extraction');
      }

      // Regex fallback for extracting values
      const typeMatch = aiResponse.match(/"type"\s*:\s*"([^"]+)"/);
      const confidenceMatch = aiResponse.match(/"confidence"\s*:\s*([0-9.]+)/);

      const extractedType = typeMatch ? typeMatch[1] : 'neutral';
      const extractedConfidence = confidenceMatch ? parseFloat(confidenceMatch[1]) : 0.5;

      console.log(`Response type fallback: ${extractedType} (${(extractedConfidence * 100).toFixed(0)}%)`);

      return {
        type: extractedType as 'positive' | 'negative' | 'neutral',
        confidence: Math.max(0, Math.min(1, extractedConfidence)),
        reasoning: 'Regex extraction due to malformed JSON'
      };
    } catch (fallbackError) {
      console.error('All parsing methods failed:', fallbackError);

      // Final fallback: return neutral response
      return {
        type: 'neutral',
        confidence: 0.3,
        reasoning: 'All parsing failed, using default neutral response'
      };
    }
  }
}

/**
 * Determines response type based on user input using GPT-based classification
 */
export async function getResponseType(text: string): Promise<'positive' | 'negative' | 'neutral'> {
  try {
    const result = await classifyResponseType(text);
    return result.type;
  } catch (error) {
    console.error('GPT response type classification failed, using fallback:', error);
    // Fallback to basic pattern matching if GPT fails
    if (PATTERNS.positive.test(text.trim())) return 'positive';
    if (PATTERNS.negative.test(text.trim())) return 'negative';
    return 'neutral';
  }
}

// ============================================================================
// Company Data Access Functions
// ============================================================================

/**
 * Safe company name lookup
 */
export function getCompanyName(ticker: string): string {
  const company = (DATA as any)[ticker];
  return company ? company.name : ticker;
}

/**
 * Gets complete company data safely
 */
export function getCompanyData(ticker: string): CompanyData | null {
  const company = (DATA as any)[ticker];
  return company ? {
    name: company.name,
    industry: company.industry,
    description: company.description
  } : null;
}

/**
 * Checks if a ticker exists in the dataset
 */
export function isValidTicker(ticker: string): boolean {
  return ticker in DATA;
}

/**
 * Gets all available tickers
 */
export function getAllTickers(): string[] {
  return Object.keys(DATA);
}

/**
 * Gets companies by industry
 */
export function getCompaniesByIndustry(industry: string): Array<{ticker: string, data: CompanyData}> {
  const companies: Array<{ticker: string, data: CompanyData}> = [];
  
  for (const [ticker, company] of Object.entries(DATA)) {
    const comp = company as any;
    if (comp.industry === industry) {
      companies.push({
        ticker,
        data: {
          name: comp.name,
          industry: comp.industry,
          description: comp.description
        }
      });
    }
  }
  
  return companies;
}

// ============================================================================
// Random Recommendation Generation
// ============================================================================

/**
 * Generates random investment recommendation (performance optimized)
 */
export function generateRandomRecommendation(): RandomRecommendation {
  const allIndustries = getAllAvailableIndustries();
  const randomIndustry = allIndustries[Math.floor(Math.random() * allIndustries.length)];

  // Efficiently collect companies in the industry
  const industryCompanies: CompanyRecommendation[] = [];

  for (const [ticker, company] of Object.entries(DATA)) {
    const comp = company as any;
    if (comp.industry === randomIndustry) {
      industryCompanies.push({
        ticker,
        name: comp.name,
        description: comp.description
      });
    }
  }

  // Fisher-Yates shuffle algorithm for performance optimization
  for (let i = industryCompanies.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [industryCompanies[i], industryCompanies[j]] = [industryCompanies[j], industryCompanies[i]];
  }

  return {
    industry: randomIndustry,
    companies: industryCompanies.slice(0, 3)
  };
}

/**
 * Generates multiple random recommendations
 */
export function generateMultipleRecommendations(count: number): RandomRecommendation[] {
  const recommendations: RandomRecommendation[] = [];
  const usedIndustries = new Set<string>();
  
  for (let i = 0; i < count; i++) {
    let recommendation: RandomRecommendation;
    let attempts = 0;
    
    do {
      recommendation = generateRandomRecommendation();
      attempts++;
    } while (usedIndustries.has(recommendation.industry) && attempts < 10);
    
    usedIndustries.add(recommendation.industry);
    recommendations.push(recommendation);
  }
  
  return recommendations;
}

// ============================================================================
// Company Name and Description Utilities
// ============================================================================

/**
 * Formats company name with ticker for display
 */
export function formatCompanyDisplay(ticker: string, includeIndustry: boolean = false): string {
  const company = getCompanyData(ticker);
  if (!company) return ticker;
  
  const base = `${company.name} (${ticker})`;
  return includeIndustry ? `${base} - ${company.industry}` : base;
}

/**
 * Creates a formatted company list for display
 */
export function formatCompanyList(
  tickers: string[], 
  numbered: boolean = true,
  includeIndustry: boolean = false
): string {
  return tickers
    .map((ticker, index) => {
      const prefix = numbered ? `${index + 1}. ` : '• ';
      return prefix + formatCompanyDisplay(ticker, includeIndustry);
    })
    .join('\n');
}

/**
 * Translates and formats company recommendations
 */
export async function translateAndFormatRecommendations(
  recommendations: CompanyRecommendation[]
): Promise<Array<CompanyRecommendation & { translatedDescription: string }>> {
  const translatedCompanies = await Promise.all(
    recommendations.map(async (company) => ({
      ...company,
      translatedDescription: await translateDescription(company.description)
    }))
  );
  
  return translatedCompanies;
}

/**
 * Formats company descriptions for display (avoiding name duplication)
 */
export function formatCompanyDescriptions(
  companies: Array<CompanyRecommendation & { translatedDescription: string }>
): string {
  return companies
    .map(company => {
      // Check if company name is included in description
      const companyNameInDescription = company.translatedDescription.includes(company.name.split(' ')[0]);
      if (companyNameInDescription) {
        return `${company.name}(${company.ticker}) : ${company.translatedDescription}`;
      } else {
        return `${company.name}(${company.ticker})는 ${company.translatedDescription}`;
      }
    })
    .join('\n\n');
}

// ============================================================================
// Industry Statistics and Analysis
// ============================================================================

/**
 * Gets industry statistics
 */
export function getIndustryStats(): Record<string, number> {
  const industryCount: Record<string, number> = {};
  
  for (const company of Object.values(DATA)) {
    const comp = company as any;
    industryCount[comp.industry] = (industryCount[comp.industry] || 0) + 1;
  }
  
  return industryCount;
}

/**
 * Gets top industries by company count
 */
export function getTopIndustries(limit: number = 10): Array<{industry: string, count: number}> {
  const stats = getIndustryStats();
  
  return Object.entries(stats)
    .map(([industry, count]) => ({ industry, count }))
    .sort((a, b) => b.count - a.count)
    .slice(0, limit);
}

/**
 * Gets companies with longest descriptions (potentially most detailed)
 */
export function getCompaniesWithDetailedDescriptions(limit: number = 10): Array<{
  ticker: string;
  name: string;
  industry: string;
  descriptionLength: number;
}> {
  const companies = Object.entries(DATA)
    .map(([ticker, company]) => {
      const comp = company as any;
      return {
        ticker,
        name: comp.name,
        industry: comp.industry,
        descriptionLength: comp.description.length
      };
    })
    .sort((a, b) => b.descriptionLength - a.descriptionLength)
    .slice(0, limit);
    
  return companies;
}

// ============================================================================
// Search and Filtering Utilities
// ============================================================================

/**
 * Searches companies by partial name match
 */
export function searchCompaniesByName(query: string, limit: number = 10): Array<{
  ticker: string;
  name: string;
  industry: string;
  relevanceScore: number;
}> {
  const normalizedQuery = query.toLowerCase().trim();
  const results: Array<{
    ticker: string;
    name: string;
    industry: string;
    relevanceScore: number;
  }> = [];
  
  for (const [ticker, company] of Object.entries(DATA)) {
    const comp = company as any;
    const normalizedName = comp.name.toLowerCase();
    
    let relevanceScore = 0;
    
    // Exact match gets highest score
    if (normalizedName === normalizedQuery) {
      relevanceScore = 100;
    }
    // Starts with query gets high score
    else if (normalizedName.startsWith(normalizedQuery)) {
      relevanceScore = 80;
    }
    // Contains query gets medium score
    else if (normalizedName.includes(normalizedQuery)) {
      relevanceScore = 60;
    }
    // Word match gets lower score
    else {
      const nameWords = normalizedName.split(' ');
      const queryWords = normalizedQuery.split(' ');
      
      for (const queryWord of queryWords) {
        for (const nameWord of nameWords) {
          if (nameWord.includes(queryWord) && queryWord.length > 2) {
            relevanceScore = Math.max(relevanceScore, 40);
          }
        }
      }
    }
    
    if (relevanceScore > 0) {
      results.push({
        ticker,
        name: comp.name,
        industry: comp.industry,
        relevanceScore
      });
    }
  }
  
  return results
    .sort((a, b) => b.relevanceScore - a.relevanceScore)
    .slice(0, limit);
}

/**
 * Filters companies by industry and optional name query
 */
export function filterCompanies(
  industry?: string,
  nameQuery?: string,
  limit: number = 50
): Array<{ticker: string, data: CompanyData}> {
  let companies = Object.entries(DATA).map(([ticker, company]) => {
    const comp = company as any;
    return {
      ticker,
      data: {
        name: comp.name,
        industry: comp.industry,
        description: comp.description
      }
    };
  });
  
  // Filter by industry if specified
  if (industry) {
    companies = companies.filter(({ data }) => data.industry === industry);
  }
  
  // Filter by name query if specified
  if (nameQuery) {
    const normalizedQuery = nameQuery.toLowerCase().trim();
    companies = companies.filter(({ data }) => 
      data.name.toLowerCase().includes(normalizedQuery)
    );
  }
  
  return companies.slice(0, limit);
}

// ============================================================================
// Data Validation Utilities
// ============================================================================

/**
 * Validates company data integrity
 */
export function validateCompanyData(): {
  totalCompanies: number;
  validCompanies: number;
  invalidCompanies: string[];
  missingFields: Record<string, string[]>;
} {
  const totalCompanies = Object.keys(DATA).length;
  let validCompanies = 0;
  const invalidCompanies: string[] = [];
  const missingFields: Record<string, string[]> = {};
  
  for (const [ticker, company] of Object.entries(DATA)) {
    const comp = company as any;
    const missing: string[] = [];
    
    if (!comp.name || comp.name.trim() === '') missing.push('name');
    if (!comp.industry || comp.industry.trim() === '') missing.push('industry');
    if (!comp.description || comp.description.trim() === '') missing.push('description');
    
    if (missing.length > 0) {
      invalidCompanies.push(ticker);
      missingFields[ticker] = missing;
    } else {
      validCompanies++;
    }
  }
  
  return {
    totalCompanies,
    validCompanies,
    invalidCompanies,
    missingFields
  };
}

/**
 * Gets dataset statistics
 */
export function getDatasetStats(): {
  totalCompanies: number;
  totalIndustries: number;
  averageDescriptionLength: number;
  longestCompanyName: string;
  shortestCompanyName: string;
} {
  const companies = Object.values(DATA) as any[];
  const industries = new Set(companies.map(c => c.industry));
  
  const descriptionLengths = companies.map(c => c.description.length);
  const averageDescriptionLength = descriptionLengths.reduce((a, b) => a + b, 0) / descriptionLengths.length;
  
  const companyNames = companies.map(c => c.name);
  const longestCompanyName = companyNames.reduce((a, b) => a.length > b.length ? a : b);
  const shortestCompanyName = companyNames.reduce((a, b) => a.length < b.length ? a : b);
  
  return {
    totalCompanies: companies.length,
    totalIndustries: industries.size,
    averageDescriptionLength: Math.round(averageDescriptionLength),
    longestCompanyName,
    shortestCompanyName
  };
}
